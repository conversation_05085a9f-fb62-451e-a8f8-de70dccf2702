# 🤖 ERP Report Agent Execution Prompt

## 🎯 **AGENT INSTRUCTIONS**

You are an ERP expert agent tasked with implementing a new report API following the proven Bang Ke Hoa Don Chung Tu Hang Hoa Dich Vu Ban Ra pattern.

---

## 📋 **INPUT TEMPLATE FOR USERS**

````markdown
I want to create an ERP report API with the following specifications:

**1. Report Name:** [EXACT_REPORT_NAME]

**2. cURL Request (POST method):**

```bash
[PASTE_COMPLETE_CURL_COMMAND_HERE]
```
````

**3. Response Fields Required:**

```json
["stt", "field_name_1", "field_name_2", "field_name_3"]
```

**4. Module Structure:**

- Module: [MODULE_NAME] (e.g., ton_kho, tai_san, ban_hang)
- Sub-module: [SUB_MODULE_NAME] (e.g., tinh_hinh_nhap_xuat_kho)
- Report name: [REPORT_NAME] (e.g., bang_ke_nhap_xuat_kho)

**5. Expected Endpoint:**
`/api/entities/{entity_slug}/erp/[module]/[sub-module]/[report-name]/`

Please implement following the Bang Ke Hoa Don Chung Tu Hang Hoa Dich Vu Ban Ra pattern.

````

---

## 🚀 **AGENT EXECUTION WORKFLOW**

### **STEP 0: Deep Analysis (MANDATORY - NEW)**

Execute these enhanced analysis commands:

```bash
# 1. Database Schema Deep Dive
python manage.py shell -c "
from django.db import connection
cursor = connection.cursor()

# Check ALL related tables with detailed info
tables = ['[MAIN_TABLE]', '[DETAIL_TABLE]', 'django_ledger_customermodel', 'group']
for table in tables:
    try:
        cursor.execute(f'PRAGMA table_info({table})')
        print(f'\\n{table} structure:')
        for row in cursor.fetchall():
            print(f'  {row}')

        # Check foreign keys
        cursor.execute(f'PRAGMA foreign_key_list({table})')
        fks = cursor.fetchall()
        if fks:
            print(f'  Foreign keys: {fks}')

        # Check sample data
        cursor.execute(f'SELECT COUNT(*) FROM {table}')
        count = cursor.fetchone()[0]
        print(f'  Record count: {count}')

    except Exception as e:
        print(f'  {table}: ERROR - {e}')
"

# 2. Business Logic Analysis
python manage.py shell -c "
# Check for existing similar reports
from django_ledger.services import *
import os
print('\\nExisting report patterns:')
for root, dirs, files in os.walk('django_ledger/services'):
    if 'bang_ke' in root or 'bao_cao' in root:
        print(f'  Pattern: {root}')
"

# 3. Entity and Dependencies Validation
python manage.py shell -c "
from django_ledger.models import EntityModel, CustomerModel
entity = EntityModel.objects.filter(slug='my-new-company-adlel3b6').first()
print(f'\\nEntity: {entity.name if entity else \"Not found\"}')
if entity:
    customers = CustomerModel.objects.filter(entity_model=entity).count()
    print(f'Existing customers: {customers}')
    print(f'Entity UUID: {entity.uuid}')
"
````

### **STEP 1: Enhanced Analysis & Planning**

Execute these commands in order:

```bash
# 1. Analyze database schema with business understanding
python manage.py shell -c "
from django.db import connection
with connection.cursor() as cursor:
    # Get complete table definition
    cursor.execute('SELECT sql FROM sqlite_master WHERE name=\"[MAIN_TABLE_NAME]\"')
    print('Main table definition:')
    for row in cursor.fetchall():
        print(row)

    cursor.execute('SELECT sql FROM sqlite_master WHERE name=\"[DETAIL_TABLE_NAME]\"')
    print('\\nDetail table definition:')
    for row in cursor.fetchall():
        print(row)

    # Check constraints and indexes
    cursor.execute('PRAGMA index_list([MAIN_TABLE_NAME])')
    print('\\nIndexes:', cursor.fetchall())
"

# 2. Check existing similar patterns with detailed analysis
find . -name "*bang_ke_hoa_don*" -type f | head -10
echo \"\\nAnalyzing working pattern:\"
ls -la django_ledger/services/thue/bang_ke_thue/bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra/

# 3. Validate entity and test data requirements
python manage.py shell -c "
from django_ledger.models import EntityModel
entity = EntityModel.objects.filter(slug='my-new-company-adlel3b6').first()
print(f'Entity found: {entity.name if entity else \"Not found\"}')
if entity:
    print(f'Entity UUID: {entity.uuid}')
    print(f'Entity slug: {entity.slug}')
"
```

### **STEP 2: Implementation (Follow Exact Pattern)**

**2.1 Create Service Structure:**

```bash
# Create folder structure
mkdir -p django_ledger/services/[module]/[sub_module]/[report_name]/utils
mkdir -p django_ledger/api/serializers/[module]/[sub_module]/[report_name]
mkdir -p django_ledger/api/views/[module]/[sub_module]/[report_name]
```

**2.2 Copy and Modify from Bang Ke Hoa Don:**

```bash
# Copy service pattern
cp django_ledger/services/thue/bang_ke_thue/bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra/bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra.py \
   django_ledger/services/[module]/[sub_module]/[report_name]/[report_name].py

# Copy utils structure
cp -r django_ledger/services/thue/bang_ke_thue/bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra/utils/* \
      django_ledger/services/[module]/[sub_module]/[report_name]/utils/

# Copy test data pattern
cp django_ledger/services/thue/bang_ke_thue/bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra/create_bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra_test_data.py \
   django_ledger/services/[module]/[sub_module]/[report_name]/create_[report_name]_test_data.py
```

**2.3 Modify Files (Replace Placeholders):**

In each copied file, replace:

- `BangKeHoaDonChungTuHangHoaDichVuBanRa` → `[YourReportName]`
- `bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra` → `[your_report_name]`
- `hoa_don_ban_hang` → `[your_main_table]`
- `chi_tiet_hoa_don` → `[your_detail_table]`

### **STEP 3: Database Schema Adaptation (CRITICAL)**

```python
# In utils/sql_queries.py - Update SQL query
MAIN_REPORT_QUERY = """
    SELECT
        main.[field1] as [alias1],
        main.[field2] as [alias2],
        detail.[field3] as [alias3]
    FROM [main_table] main
    INNER JOIN [detail_table] detail ON main.uuid = detail.[main_table]_id
    WHERE {conditions}
    ORDER BY main.[date_field], main.[doc_field]
"""

# In utils/data_transformers.py - Update field mapping
def transform_report_item(item: Dict[str, Any], index: int) -> Dict[str, Any]:
    return {
        'stt': index,
        '[field1]': item.get('[alias1]', ''),
        '[field2]': float(item.get('[alias2]', 0)),
        '[field3]': item.get('[alias3]', ''),
        # Map ALL response fields from user specification
    }
```

### **STEP 4: Test Data Creation (MANDATORY)**

```python
# In create_[report_name]_test_data.py
def create_master_data(entity_uuid):
    # Create required master data for YOUR tables
    # Follow dependency order: Entity → Basic deps → Master → Transactions

def create_comprehensive_transaction(entity_uuid, master_data, transaction_date, doc_number):
    # Create main record with ALL required fields
    # Create detail records with proper relationships
    # Use realistic business data
```

### **STEP 5: API Layer (Copy Pattern)**

```python
# Copy serializer pattern
cp django_ledger/api/serializers/thue/bang_ke_thue/bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra/bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra.py \
   django_ledger/api/serializers/[module]/[sub_module]/[report_name]/[report_name].py

# Copy viewset pattern
cp django_ledger/api/views/thue/bang_ke_thue/bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra/bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra.py \
   django_ledger/api/views/[module]/[sub_module]/[report_name]/[report_name].py
```

### **STEP 6: Enhanced Testing & Validation (MANDATORY)**

```bash
# 1. Create comprehensive test data with validation
cd django_ledger/services/[module]/[sub_module]/[report_name]
python create_[report_name]_test_data.py my-new-company-adlel3b6 --count 10

# 2. Validate test data creation
python manage.py shell -c "
from django.db import connection
cursor = connection.cursor()
cursor.execute('SELECT COUNT(*) FROM [MAIN_TABLE] WHERE entity_model_id = \"[ENTITY_UUID]\"')
main_count = cursor.fetchone()[0]
cursor.execute('SELECT COUNT(*) FROM [DETAIL_TABLE]')
detail_count = cursor.fetchone()[0]
print(f'Created records - Main: {main_count}, Detail: {detail_count}')
"

# 3. Test API with multiple scenarios
echo \"Testing basic request...\"
curl -X POST 'http://localhost:8003/api/entities/my-new-company-adlel3b6/erp/[module]/[sub-module]/[report-name]/' \
  -u 'admin:minhdang123' \
  -H 'Content-Type: application/json' \
  -d '[USER_PROVIDED_JSON_FILTERS]' | python -c "
import sys, json
data = json.load(sys.stdin)
print(f'Status: OK, Count: {data.get(\"count\", 0)}')
if data.get('results'):
    first = data['results'][0]
    print(f'Fields: {list(first.keys())}')
    print(f'Sample: {first}')
"

# 4. Test with test parameter (if implemented)
echo \"Testing with test parameter...\"
curl -X POST 'http://localhost:8003/api/entities/my-new-company-adlel3b6/erp/[module]/[sub-module]/[report-name]/' \
  -u 'admin:minhdang123' \
  -H 'Content-Type: application/json' \
  -d '{"test": "true", [OTHER_FILTERS]}' | python -c "
import sys, json
data = json.load(sys.stdin)
print(f'Test mode - Count: {data.get(\"count\", 0)}')
"

# 5. Performance testing
echo \"Performance testing...\"
time curl -X POST 'http://localhost:8003/api/entities/my-new-company-adlel3b6/erp/[module]/[sub-module]/[report-name]/' \
  -u 'admin:minhdang123' \
  -H 'Content-Type: application/json' \
  -d '[USER_PROVIDED_JSON_FILTERS]' > /dev/null

# 6. Comprehensive validation
# - Status: 200 OK
# - Count: > 0
# - Fields: All user-specified fields present
# - Response time: < 2 seconds
# - No null/empty fields
# - Business logic compliance
```

---

## ✅ **ENHANCED SUCCESS VALIDATION CHECKLIST**

Before marking complete, verify ALL items:

### **Phase 0: Deep Analysis Completed**

- [ ] **Database Schema Deep Dive**: All tables analyzed with PRAGMA table_info
- [ ] **Foreign Key Mapping**: All relationships mapped and understood
- [ ] **Business Logic Understanding**: Calculation rules and constraints clear
- [ ] **Existing Pattern Study**: Working report pattern identified and analyzed

### **Phase 1: Implementation Quality**

- [ ] **Service Implementation**: Copied and adapted from working pattern
- [ ] **Utils Structure**: Enhanced 3-file structure (sql_queries, test_data_utils, data_transformers)
- [ ] **Error Handling**: Comprehensive error handling implemented
- [ ] **Import Management**: Clean imports with error handling

### **Phase 2: Test Data Excellence**

- [ ] **Comprehensive Test Data**: Creates main + detail records with ALL required fields
- [ ] **Business Logic Compliance**: Test data follows business rules (balance equations, etc.)
- [ ] **Error Handling**: IntegrityError and foreign key violations handled
- [ ] **Data Validation**: Test data integrity validated

### **Phase 3: API Quality Assurance**

- [ ] **API Response**: Returns 200 with expected fields
- [ ] **Performance**: Response time < 2 seconds consistently
- [ ] **Data Quality**: No null/empty fields in response
- [ ] **Field Completeness**: All user-specified fields present and correct
- [ ] **Test Parameter**: Test mode working (if implemented)
- [ ] **Multiple Scenarios**: Basic, filtered, and edge case testing completed

---

## 🚨 **COMMON PITFALLS TO AVOID**

1. **❌ Don't create incomplete transactions** - Always main + detail
2. **❌ Don't skip schema validation** - Check field names exactly
3. **❌ Don't use simplified test data** - Create comprehensive realistic data
4. **❌ Don't forget entity filtering** - Always filter by entity_model_id first
5. **❌ Don't skip immediate testing** - Test API right after data creation

---

## 🎯 **AGENT SUCCESS CRITERIA**

**ONLY mark task complete when:**

1. ✅ API returns 200 status with data
2. ✅ All user-specified response fields present
3. ✅ Test data creation script works
4. ✅ Response time < 2 seconds
5. ✅ No errors in implementation

**Report implementation following this pattern ensures enterprise-grade quality.** 🏆
