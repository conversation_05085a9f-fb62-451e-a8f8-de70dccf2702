# 🏆 ERP Report API Template - Master Reference

## 📊 **Template Overview**

Enterprise-grade template for creating ERP report APIs based on successful implementation of Bang Ke Hoa Don Chung Tu Hang Hoa Dich Vu Ban Ra.

---

## 🎯 **TEMPLATE CLASSIFICATION**

### **Level 1: Simple Reports (< 10 fields, 1-2 tables)**

```
Structure: Single service file + basic test data
Complexity: Low
Timeline: 1-2 days
```

### **Level 2: Standard Reports (10-25 fields, 2-5 tables)**

```
Structure: Service + Utils (3-file) + comprehensive test data
Complexity: Medium
Timeline: 3-5 days
```

### **Level 3: Complex Reports (25+ fields, 5+ tables)**

```
Structure: Full enterprise pattern + advanced utils + validation
Complexity: High
Timeline: 1-2 weeks
```

---

## 🏗️ **STANDARD FOLDER STRUCTURE**

```
services/[module]/[sub_module]/[report_name]/
├── __init__.py
├── [report_name].py                    # Main service class
├── utils/                              # 3-file utils structure
│   ├── __init__.py                     # Clean exports
│   ├── sql_queries.py                  # SQL templates & constants
│   ├── query_builders.py               # Dynamic query building
│   └── data_transformers.py            # Data transformation logic
├── create_[report_name]_test_data.py   # Comprehensive test data
├── [report_name]_query.sql             # SQL documentation
└── warning.md                          # Implementation notes

api/serializers/[module]/[sub_module]/[report_name]/
├── __init__.py
└── [report_name].py                    # Request & Response serializers

api/views/[module]/[sub_module]/[report_name]/
├── __init__.py
└── [report_name].py                    # ViewSet implementation

api/routers/[module]/[sub_module]/[report_name]/
└── urls.py                             # URL routing
```

---

## 🔧 **CORE IMPLEMENTATION PATTERNS**

### **1. Service Pattern (Proven)**

```python
class [ReportName]Service(BaseService):
    """Service for [ReportName] business logic."""

    def __init__(self):
        super().__init__()
        # Minimal dependencies - avoid complex lazy imports

    def generate_report(self, entity_slug: str, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Main orchestration method."""
        try:
            # Get filtered data using utils
            data = self._get_filtered_data(entity_slug, filters)
            # Process and format using utils
            return self._process_report_data(data)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error generating report: {str(e)}", exc_info=True)
            return []

    def _get_filtered_data(self, entity_slug: str, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get filtered data using direct SQL with utils."""
        from django.db import connection
        from django_ledger.models import EntityModel
        from .utils.query_builders import build_complete_query

        # Get entity UUID - always filter by entity first
        try:
            entity = EntityModel.objects.get(slug=entity_slug)
            entity_uuid = str(entity.uuid).replace('-', '')
        except EntityModel.DoesNotExist:
            return []

        # Build complete query using utils
        sql_query, sql_params = build_complete_query(entity_uuid, filters)

        # Execute query with proper error handling
        with connection.cursor() as cursor:
            cursor.execute(sql_query, sql_params)
            columns = [col[0] for col in cursor.description]
            results = []

            for row in cursor.fetchall():
                row_dict = dict(zip(columns, row))
                results.append(row_dict)

            return results

    def _process_report_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process data using utils transformers."""
        from .utils.data_transformers import transform_report_data
        return transform_report_data(data)
```

### **2. Utils Pattern (3-File Structure)**

```python
# utils/sql_queries.py - SQL templates & constants
MAIN_REPORT_QUERY = """
    SELECT
        main.field1 as field1,
        main.field2 as field2,
        detail.field3 as field3
    FROM main_table main
    INNER JOIN detail_table detail ON main.uuid = detail.main_id
    WHERE {conditions}
    ORDER BY main.date_field, main.doc_number
"""

# utils/query_builders.py - Dynamic query building
def build_complete_query(entity_uuid: str, filters: Dict[str, Any]) -> Tuple[str, List[Any]]:
    """Build complete SQL query with all conditions."""
    # Always start with entity filter
    all_conditions = ["main.entity_model_id = %s"]
    all_params = [entity_uuid]

    # Add dynamic conditions based on filters
    # Return complete query and parameters

# utils/data_transformers.py - Data transformation
def transform_report_data(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Transform list of data to API response format."""
    return [
        transform_report_item(item, index)
        for index, item in enumerate(data, 1)
    ]
```

### **3. Test Data Pattern (Comprehensive)**

```python
#!/usr/bin/env python3
"""
Comprehensive ERP Report Test Data Creation Script
"""

def get_db_connection():
    """Get database connection with correct path handling."""
    db_path = os.path.join(os.path.dirname(__file__), '../../../../../db.sqlite3')
    return sqlite3.connect(db_path)

def create_master_data(entity_uuid):
    """Create comprehensive master data for ERP system."""
    # 1. Create required master data with ALL fields
    # 2. Handle foreign key relationships properly
    # 3. Use proper business logic for amounts and dates
    # 4. Return structured master data for transaction creation

def create_comprehensive_transaction(entity_uuid, master_data, transaction_date, doc_number):
    """Create comprehensive transaction with ALL required fields."""
    # 1. Generate proper UUIDs
    # 2. Use master data for relationships
    # 3. Calculate business logic (taxes, totals)
    # 4. Insert with ALL table fields (not just a few)
    # 5. Create both main and detail records

def test_api_with_data(entity_slug):
    """Test API with created data immediately."""
    # Automated API testing right after data creation
```

---

## 📋 **IMPLEMENTATION CHECKLIST**

### **Phase 1: Schema Analysis (CRITICAL)**

- [ ] **Database Schema Validation** - `sqlite3 db.sqlite3 ".schema table_name" "db_schema.txt"`
- [ ] **Field Type Validation** - UUID vs String vs Integer vs Decimal
- [ ] **Identify Main + Detail Tables** - Critical for transaction reports
- [ ] **Foreign Key Dependencies** - Map all relationships
- [ ] **Required Fields Analysis** - `PRAGMA table_info(table_name)`

### **Phase 2: API Structure**

- [ ] **RequestSerializer** - All filter parameters with validation
- [ ] **ResponseSerializer** - All response fields with proper types
- [ ] **Service Class** - Main business logic with error handling
- [ ] **ViewSet** - get_report method with ERPPagination
- [ ] **URL Routing** - POST method mapping

### **Phase 3: Utils Implementation**

- [ ] **sql_queries.py** - SQL templates and field mappings
- [ ] **query_builders.py** - Dynamic WHERE clause building
- [ ] **data_transformers.py** - Complete field transformation

### **Phase 4: Test Data Creation**

- [ ] **Database Path Handling** - Relative path: `../../../../../db.sqlite3`
- [ ] **Dependency Creation Order** - Entity → Basic deps → Master → Transactions
- [ ] **Complete Transaction Creation** - ALWAYS create main + detail records
- [ ] **All Required Fields** - Check PRAGMA table_info for NOT NULL constraints
- [ ] **Immediate API Testing** - Test API right after data creation

### **Phase 5: Validation**

- [ ] **API Response 200** - Successful data retrieval
- [ ] **Response Time < 2s** - Performance requirement
- [ ] **No Null Fields** - Complete data transformation
- [ ] **Proper Pagination** - ERPPagination format
- [ ] **All Filters Working** - Test each filter parameter

---

## 🎯 **SUCCESS CRITERIA**

### **Technical Requirements:**

- ✅ API returns 200 status consistently
- ✅ Response time < 2 seconds
- ✅ No null/empty fields in response
- ✅ Proper pagination format
- ✅ All serializer fields present

### **Data Quality Requirements:**

- ✅ All table constraints satisfied
- ✅ Proper foreign key relationships
- ✅ Business logic compliance
- ✅ Realistic test data volumes (10+ records minimum)
- ✅ Complete transaction records (main + detail)

### **Code Quality Requirements:**

- ✅ Utils properly organized (3-file structure)
- ✅ Error handling implemented
- ✅ Database path handling correct
- ✅ Clean imports and dependencies

---

## 🚀 **TEMPLATE USAGE**

1. **Copy folder structure** exactly as specified
2. **Replace placeholders** with actual report names
3. **Follow implementation checklist** step by step
4. **Validate against success criteria** before completion
5. **Document any deviations** in warning.md

**This template ensures consistent, enterprise-grade ERP report implementations.** 🏆
