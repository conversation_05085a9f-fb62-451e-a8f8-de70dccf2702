"""
🔍 SQL QUERIES UTILS FOR DEBT BALANCE REPORT
============================================
Centralized SQL queries for customer debt balance calculations.
"""

from typing import Dict, Any


class DebtBalanceSQLQueries:
    """
    Utility class containing all SQL queries for debt balance report.
    Separates complex SQL logic from service layer.
    """

    @staticmethod
    def build_customer_debt_balance_query(parsed_filters: Dict[str, Any]) -> str:
        """
        Build the main customer debt balance SQL query.

        Parameters
        ----------
        parsed_filters : Dict[str, Any]
            Parsed filter parameters

        Returns
        -------
        str
            Complete SQL query string
        """
        # Build dynamic filter conditions
        account_filter = DebtBalanceSQLQueries._build_account_filter(parsed_filters)
        customer_filter = DebtBalanceSQLQueries._build_customer_filter(parsed_filters)
        nhom_filter = DebtBalanceSQLQueries._build_nhom_filter(parsed_filters)

        # Main query with CTE for better performance
        sql_query = f'''
            WITH customer_balances AS (
                SELECT
                    cust.customer_code as ma_kh,
                    cust.customer_name as ten_kh,

                    -- Real transaction calculations for debt balance
                    COALESCE(SUM(CASE
                        WHEN txn.tx_type = 'debit' AND je.date < %(start_date)s
                        THEN txn.amount ELSE 0
                    END), 0) as no_dk,

                    COALESCE(SUM(CASE
                        WHEN txn.tx_type = 'credit' AND je.date < %(start_date)s
                        THEN txn.amount ELSE 0
                    END), 0) as co_dk,

                    COALESCE(SUM(CASE
                        WHEN txn.tx_type = 'debit' AND je.date BETWEEN %(start_date)s AND %(end_date)s
                        THEN txn.amount ELSE 0
                    END), 0) as ps_no,

                    COALESCE(SUM(CASE
                        WHEN txn.tx_type = 'credit' AND je.date BETWEEN %(start_date)s AND %(end_date)s
                        THEN txn.amount ELSE 0
                    END), 0) as ps_co,

                    -- Customer group data (optimized with single join)
                    COALESCE(n1.ma_nhom, '') as nhom,
                    COALESCE(n1.ten_phan_nhom, '') as nhom1,
                    COALESCE(n2.ten_phan_nhom, '') as nhom2,
                    COALESCE(n3.ten_phan_nhom, '') as nhom3

                FROM django_ledger_customermodel cust
                LEFT JOIN `group` n1 ON cust.customer_group1_id = n1.uuid
                LEFT JOIN `group` n2 ON cust.customer_group2_id = n2.uuid
                LEFT JOIN `group` n3 ON cust.customer_group3_id = n3.uuid
                LEFT JOIN django_ledger_transactionmodel txn ON txn.description LIKE CONCAT('%', cust.customer_code, '%')
                LEFT JOIN journal_entry je ON txn.journal_entry_id = je.uuid
                LEFT JOIN django_ledger_accountmodel acc ON txn.account_id = acc.uuid

                WHERE cust.entity_model_id = %(entity_uuid)s
                  AND ({account_filter} OR acc.code IS NULL)
                  {customer_filter}
                  {nhom_filter}

                GROUP BY
                    cust.uuid, cust.customer_code, cust.customer_name,
                    n1.ma_nhom, n1.ten_phan_nhom, n2.ten_phan_nhom, n3.ten_phan_nhom
            )
            SELECT
                ROW_NUMBER() OVER (ORDER BY ma_kh) as stt,
                '{parsed_filters['account_codes'][0] if parsed_filters['account_codes'] else '131'}' as tk,
                ma_kh,
                ten_kh,
                no_dk,
                co_dk,
                ps_no,
                ps_co,
                nhom,
                nhom1,
                nhom2,
                nhom3,
                (no_dk + ps_no - ps_co - co_dk) as no_ck,
                0.0 as co_ck

            FROM customer_balances
            WHERE (no_dk != 0 OR co_dk != 0 OR ps_no != 0 OR ps_co != 0)
            ORDER BY ma_kh
        '''

        return sql_query

    @staticmethod
    def _build_account_filter(parsed_filters: Dict[str, Any]) -> str:
        """Build account filter condition."""
        account_conditions = []
        for code in parsed_filters.get('account_codes', ['131']):
            account_conditions.append(f"acc.code LIKE '{code}%'")
        return " OR ".join(account_conditions) if account_conditions else "acc.code LIKE '131%'"

    @staticmethod
    def _build_customer_filter(parsed_filters: Dict[str, Any]) -> str:
        """Build customer filter condition."""
        customer_code = parsed_filters.get('customer_code')
        if customer_code:
            return f"AND cust.customer_code LIKE '%{customer_code}%'"
        return ""

    @staticmethod
    def _build_nhom_filter(parsed_filters: Dict[str, Any]) -> str:
        """Build nhom (customer group) filter conditions."""
        nhom_filters = []
        nhom_filter_map = parsed_filters.get('nhom_filters', {})

        for nhom_key, nhom_value in nhom_filter_map.items():
            if nhom_key == 'nhom1':
                nhom_filters.append(f"n1.ma_nhom LIKE '%{nhom_value}%'")
            elif nhom_key == 'nhom2':
                nhom_filters.append(f"n2.ma_nhom LIKE '%{nhom_value}%'")
            elif nhom_key == 'nhom3':
                nhom_filters.append(f"n3.ma_nhom LIKE '%{nhom_value}%'")

        if nhom_filters:
            return f"AND ({' AND '.join(nhom_filters)})"
        return ""

    @staticmethod
    def get_customer_count_query() -> str:
        """Get simple customer count query for validation."""
        return '''
            SELECT COUNT(*) as customer_count
            FROM django_ledger_customermodel cust
            WHERE cust.entity_model_id = %(entity_uuid)s
        '''

    @staticmethod
    def get_customer_with_groups_query() -> str:
        """Get customers with group information query."""
        return '''
            SELECT
                cust.customer_code,
                cust.customer_name,
                COALESCE(n1.ma_nhom, '') as nhom,
                COALESCE(n1.ten_phan_nhom, '') as nhom1,
                COALESCE(n2.ten_phan_nhom, '') as nhom2,
                COALESCE(n3.ten_phan_nhom, '') as nhom3
            FROM django_ledger_customermodel cust
            LEFT JOIN `group` n1 ON cust.customer_group1_id = n1.uuid
            LEFT JOIN `group` n2 ON cust.customer_group2_id = n2.uuid
            LEFT JOIN `group` n3 ON cust.customer_group3_id = n3.uuid
            WHERE cust.entity_model_id = %(entity_uuid)s
            ORDER BY cust.customer_code
        '''


# Legacy field mappings (kept for backward compatibility)
DEBT_BALANCE_FIELD_MAP = {
    'stt': 'ROW_NUMBER() OVER (ORDER BY tk, ma_kh)',
    'tk': 'acc.code',
    'ma_kh': 'cust.ma_khach_hang',
    'ten_kh': 'cust.customer_name',
    'no_dk': 'opening_debit_balance',
    'co_dk': 'opening_credit_balance',
    'ps_no': 'period_debit_transactions',
    'ps_co': 'period_credit_transactions',
    'nhom': 'cust.nh_kh1',
    'nhom1': 'cust.nh_kh2',
    'nhom2': 'cust.nh_kh3',
    'nhom3': 'empty_string',
    'no_ck': 'closing_debit_balance',
    'co_ck': 'closing_credit_balance'
}

# Base conditions that are always applied
BASE_CONDITIONS = [
    "cust.entity_model_id = %s"
]


def build_debt_balance_query(conditions: str) -> str:
    """
    Build the complete debt balance query with conditions.
    
    Parameters
    ----------
    conditions : str
        WHERE conditions string
        
    Returns
    -------
    str
        Complete SQL query
    """
    return DEBT_BALANCE_REPORT_QUERY.format(conditions=conditions)


def get_base_conditions() -> list:
    """
    Get base conditions that are always applied.
    
    Returns
    -------
    list
        List of base condition strings
    """
    return BASE_CONDITIONS.copy()


# Query for getting entity UUID
ENTITY_UUID_QUERY = """
    SELECT uuid FROM django_ledger_entitymodel WHERE slug = %s
"""

# Query for validating date range
DATE_RANGE_VALIDATION_QUERY = """
    SELECT COUNT(*) FROM django_ledger_journalentrymodel 
    WHERE entity_id = %s 
    AND date BETWEEN %s AND %s
"""

# Query for getting customer account codes
CUSTOMER_ACCOUNT_QUERY = """
    SELECT DISTINCT acc.code 
    FROM django_ledger_accountmodel acc
    WHERE acc.coa_model_id IN (
        SELECT coa.uuid FROM django_ledger_chartofaccountmodel coa 
        WHERE coa.entity_id = %s
    )
    AND acc.code LIKE '131%'
    ORDER BY acc.code
"""

# Query for getting customer groups
CUSTOMER_GROUP_QUERY = """
    SELECT DISTINCT nh_kh1, nh_kh2, nh_kh3
    FROM django_ledger_customermodel
    WHERE entity_model_id = %s
    AND (nh_kh1 IS NOT NULL OR nh_kh2 IS NOT NULL OR nh_kh3 IS NOT NULL)
    ORDER BY nh_kh1, nh_kh2, nh_kh3
"""
