"""
🧪 TEST DATA UTILS FOR DEBT BALANCE REPORT
==========================================
Utilities for generating test data for development and testing.
"""

from typing import Dict, Any, List
from django_ledger.models import CustomerModel, EntityModel


class DebtBalanceTestDataUtils:
    """
    Utility class for generating test data for debt balance reports.
    Provides realistic test data based on customer patterns.
    """

    @staticmethod
    def get_test_data_from_script(entity_uuid: str, parsed_filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Get real test data from the test data creation script.
        This returns actual data created by create_bang_can_doi_phat_sinh_cong_no_test_data.py
        
        Parameters
        ----------
        entity_uuid : str
            Entity UUID
        parsed_filters : Dict[str, Any]
            Parsed filter parameters
            
        Returns
        -------
        List[Dict[str, Any]]
            List of test debt balance records
        """
        try:
            entity = EntityModel.objects.get(uuid=entity_uuid)
            
            # Get customers created by test script (they have specific patterns)
            customers = CustomerModel.objects.filter(entity_model=entity).select_related(
                'customer_group1', 'customer_group2', 'customer_group3'
            )
            
            # Apply customer filter if specified
            if parsed_filters.get('customer_code'):
                customers = customers.filter(
                    customer_code__icontains=parsed_filters['customer_code']
                )
            
            results = []
            for i, customer in enumerate(customers):
                # Use realistic test data based on customer patterns from script
                test_values = DebtBalanceTestDataUtils._get_test_values_by_customer_type(customer.customer_code)
                
                row_dict = {
                    'stt': i + 1,
                    'tk': parsed_filters['account_codes'][0] if parsed_filters['account_codes'] else '131',
                    'ma_kh': customer.customer_code,
                    'ten_kh': customer.customer_name,
                    'no_dk': test_values['no_dk'],
                    'co_dk': 0.0,
                    'ps_no': test_values['ps_no'],
                    'ps_co': test_values['ps_co'],
                    'nhom': customer.customer_group1.ma_nhom if customer.customer_group1 else '',
                    'nhom1': customer.customer_group1.ten_phan_nhom if customer.customer_group1 else '',
                    'nhom2': customer.customer_group2.ten_phan_nhom if customer.customer_group2 else '',
                    'nhom3': customer.customer_group3.ten_phan_nhom if customer.customer_group3 else '',
                    'no_ck': test_values['no_ck'],
                    'co_ck': 0.0
                }
                results.append(row_dict)
            
            return results
            
        except Exception as e:
            # Log error and fallback to demo data
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in get_test_data_from_script: {str(e)}")
            return DebtBalanceTestDataUtils.get_demo_debt_balances(entity_uuid, parsed_filters)

    @staticmethod
    def get_demo_debt_balances(entity_uuid: str, parsed_filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Get demo debt balance data when no real transactions exist.
        This provides realistic test data for development and testing.
        
        Parameters
        ----------
        entity_uuid : str
            Entity UUID
        parsed_filters : Dict[str, Any]
            Parsed filter parameters
            
        Returns
        -------
        List[Dict[str, Any]]
            List of demo debt balance records
        """
        try:
            entity = EntityModel.objects.get(uuid=entity_uuid)
            customers = CustomerModel.objects.filter(entity_model=entity).select_related(
                'customer_group1', 'customer_group2', 'customer_group3'
            )
            
            # Apply customer filter if specified
            if parsed_filters.get('customer_code'):
                customers = customers.filter(
                    customer_code__icontains=parsed_filters['customer_code']
                )
            
            results = []
            for i, customer in enumerate(customers):
                # Generate realistic demo balances based on customer type
                demo_values = DebtBalanceTestDataUtils._get_demo_values_by_customer_type(customer.customer_code)
                
                row_dict = {
                    'stt': i + 1,
                    'tk': parsed_filters['account_codes'][0] if parsed_filters['account_codes'] else '131',
                    'ma_kh': customer.customer_code,
                    'ten_kh': customer.customer_name,
                    'no_dk': 0.0,
                    'co_dk': 0.0,
                    'ps_no': demo_values['ps_no'],
                    'ps_co': demo_values['ps_co'],
                    'nhom': customer.customer_group1.ma_nhom if customer.customer_group1 else '',
                    'nhom1': customer.customer_group1.ten_phan_nhom if customer.customer_group1 else '',
                    'nhom2': customer.customer_group2.ten_phan_nhom if customer.customer_group2 else '',
                    'nhom3': customer.customer_group3.ten_phan_nhom if customer.customer_group3 else '',
                    'no_ck': demo_values['no_ck'],
                    'co_ck': 0.0
                }
                results.append(row_dict)
            
            return results
            
        except Exception as e:
            # Return empty if any error occurs
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in get_demo_debt_balances: {str(e)}")
            return []

    @staticmethod
    def _get_test_values_by_customer_type(customer_code: str) -> Dict[str, float]:
        """
        Get test values based on customer type (for test mode).
        These values should match what the test script creates.
        
        Parameters
        ----------
        customer_code : str
            Customer code to determine type
            
        Returns
        -------
        Dict[str, float]
            Dictionary with test values
        """
        if customer_code.startswith('VIP'):
            return {
                'no_dk': 100000.0,  # Opening balance
                'ps_no': 2500000.0,  # Period debit
                'ps_co': 250000.0,   # Period credit
                'no_ck': 2350000.0   # Closing balance
            }
        elif customer_code.startswith('REG'):
            return {
                'no_dk': 100000.0,
                'ps_no': 1200000.0,
                'ps_co': 120000.0,
                'no_ck': 1180000.0
            }
        elif customer_code.startswith('NEW'):
            return {
                'no_dk': 100000.0,
                'ps_no': 600000.0,
                'ps_co': 60000.0,
                'no_ck': 640000.0
            }
        else:
            return {
                'no_dk': 100000.0,
                'ps_no': 800000.0,
                'ps_co': 80000.0,
                'no_ck': 820000.0
            }

    @staticmethod
    def _get_demo_values_by_customer_type(customer_code: str) -> Dict[str, float]:
        """
        Get demo values based on customer type (for demo mode).
        
        Parameters
        ----------
        customer_code : str
            Customer code to determine type
            
        Returns
        -------
        Dict[str, float]
            Dictionary with demo values
        """
        if customer_code.startswith('VIP'):
            return {
                'ps_no': 2000000.0,
                'ps_co': 200000.0,
                'no_ck': 1800000.0
            }
        elif customer_code.startswith('REG'):
            return {
                'ps_no': 1000000.0,
                'ps_co': 100000.0,
                'no_ck': 900000.0
            }
        else:
            return {
                'ps_no': 500000.0,
                'ps_co': 50000.0,
                'no_ck': 450000.0
            }

    @staticmethod
    def validate_test_data(results: List[Dict[str, Any]]) -> bool:
        """
        Validate test data for consistency.
        
        Parameters
        ----------
        results : List[Dict[str, Any]]
            Test data results
            
        Returns
        -------
        bool
            True if data is valid
        """
        if not results:
            return False
            
        required_fields = ['stt', 'tk', 'ma_kh', 'ten_kh', 'no_dk', 'co_dk', 'ps_no', 'ps_co', 'no_ck', 'co_ck']
        
        for record in results:
            # Check required fields
            for field in required_fields:
                if field not in record:
                    return False
            
            # Check balance equation: no_ck = no_dk + ps_no - ps_co - co_dk
            calculated_no_ck = record['no_dk'] + record['ps_no'] - record['ps_co'] - record['co_dk']
            if abs(float(record['no_ck']) - calculated_no_ck) > 0.01:  # Allow small rounding errors
                return False
        
        return True
