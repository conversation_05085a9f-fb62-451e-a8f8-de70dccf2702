"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Utilities package for Bang Can Doi Phat Sinh Cong No service.
"""

from .data_transformers import (
    transform_debt_balance_item,
    transform_debt_balance_data,
    calculate_balance_totals,
    group_by_account,
    group_by_customer_hierarchy,
    format_currency_amount,
    get_balance_status,
    validate_debt_balance_data,
)
from .sql_queries import (
    DebtBalanceSQLQueries,
    DEBT_BALANCE_FIELD_MAP,
)
from .test_data_utils import (
    DebtBalanceTestDataUtils,
)

# Legacy imports (kept for backward compatibility)
try:
    from .query_builders import (
        build_period_conditions,
        build_account_conditions,
        build_customer_conditions,
        build_view_type_conditions,
        build_all_conditions,
        build_complete_query,
    )
except ImportError:
    # Legacy query builders may have import issues
    pass

__all__ = [
    # Data transformers
    'transform_debt_balance_item',
    'transform_debt_balance_data',
    'calculate_balance_totals',
    'group_by_account',
    'group_by_customer_hierarchy',
    'format_currency_amount',
    'get_balance_status',
    'validate_debt_balance_data',
    # SQL queries (new)
    'DebtBalanceSQLQueries',
    'DEBT_BALANCE_FIELD_MAP',
    # Test data utils (new)
    'DebtBalanceTestDataUtils',
    # Legacy query builders (optional)
    'build_period_conditions',
    'build_account_conditions',
    'build_customer_conditions',
    'build_view_type_conditions',
    'build_all_conditions',
    'build_complete_query',
]
