#!/usr/bin/env python3
"""
🔍 DEBUG SERVICE FOR DEBT BALANCE REPORT
========================================
Debug the service to find why it's returning 0 records.
"""

import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/Documents/erp3/erp-be')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_ledger.settings.development')
django.setup()

from django_ledger.services.ban_hang.cong_no_khach_hang.bang_can_doi_phat_sinh_cong_no import BangCanDoiPhatSinhCongNoService
from django_ledger.models import EntityModel, CustomerModel


def debug_service():
    """Debug the service step by step."""
    
    print("🔍 DEBUGGING DEBT BALANCE SERVICE")
    print("=" * 50)
    
    # Step 1: Check entity
    entity_slug = "my-new-company-zuufe21o"
    try:
        entity = EntityModel.objects.get(slug=entity_slug)
        print(f"✅ Entity found: {entity.uuid}")
    except EntityModel.DoesNotExist:
        print(f"❌ Entity not found: {entity_slug}")
        return
    
    # Step 2: Check customers
    customers = CustomerModel.objects.filter(entity_model=entity)
    print(f"✅ Customers found: {customers.count()}")
    
    for customer in customers[:3]:
        print(f"   - {customer.customer_code}: {customer.customer_name}")
        print(f"     Group1: {customer.customer_group1}")
        print(f"     Group2: {customer.customer_group2}")
        print(f"     Group3: {customer.customer_group3}")
    
    # Step 3: Test service directly
    service = BangCanDoiPhatSinhCongNoService()
    
    # Test demo method directly
    print("\n🎯 Testing demo method directly...")
    parsed_filters = {
        'start_date': '2025-01-01',
        'end_date': '2025-06-03',
        'account_codes': ['131'],
        'customer_code': None,
        'nhom_filters': {},
        'group_by': [],
        'view_type': '1'
    }
    
    try:
        demo_results = service._get_demo_debt_balances(str(entity.uuid), parsed_filters)
        print(f"✅ Demo results: {len(demo_results)} records")
        
        for result in demo_results[:3]:
            print(f"   - {result['ma_kh']}: {result['ten_kh']}")
            print(f"     ps_no: {result['ps_no']}, ps_co: {result['ps_co']}, no_ck: {result['no_ck']}")
            print(f"     nhom: '{result['nhom']}', nhom1: '{result['nhom1']}'")
            
    except Exception as e:
        print(f"❌ Demo method error: {e}")
        import traceback
        traceback.print_exc()
    
    # Step 4: Test full service
    print("\n🎯 Testing full service...")
    filters = {
        'ngay_ct1': '********',
        'ngay_ct2': '********'
    }
    
    try:
        results = service.generate_report(entity_slug, filters)
        print(f"✅ Service results: {len(results)} records")
        
        for result in results[:3]:
            print(f"   - {result['ma_kh']}: {result['ten_kh']}")
            print(f"     ps_no: {result['ps_no']}, ps_co: {result['ps_co']}, no_ck: {result['no_ck']}")
            
    except Exception as e:
        print(f"❌ Service error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_service()
