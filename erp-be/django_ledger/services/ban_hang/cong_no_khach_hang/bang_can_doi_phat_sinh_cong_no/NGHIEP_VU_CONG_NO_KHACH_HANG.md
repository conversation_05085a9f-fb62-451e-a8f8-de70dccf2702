# 📊 BẢNG CÂN ĐỐI PHÁT SINH CÔNG NỢ KHÁCH HÀNG - PHÂN TÍCH NGHIỆP VỤ

## 🎯 TỔNG QUAN CHỨC NĂNG

**Tên chức năng**: Bảng Cân Đối Phát Sinh Công Nợ ERP Kế Toán

**Mục đích**: <PERSON> dõi và báo cáo tình hình công nợ khách hàng trong một khoảng thời gian cụ thể

**Đối tượng sử dụng**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> lý tà<PERSON> ch<PERSON>, <PERSON><PERSON><PERSON><PERSON> đốc

---

## � CÁC THAM SỐ ĐẦU VÀO (REQUEST PARAMETERS)

### **📅 Tham số thời gian (Bắt buộc)**

- **ngay_ct1**: "********" - <PERSON><PERSON><PERSON> b<PERSON><PERSON> đầ<PERSON> (YYYYMMDD)
- **ngay_ct2**: "********" - <PERSON><PERSON><PERSON> k<PERSON><PERSON> thú<PERSON> (YYYYMMDD)

### **🏦 Tham số tài khoản**

- **tk**: "1312,1311,131" - <PERSON><PERSON> sách mã tài kho<PERSON>n (phân cách bằng dấu phẩy)

### **👥 Tham số khách hàng**

- **ma_kh**: "A" - Mã/tên khách hàng để lọc (tìm kiếm gần đúng)
- **nh_kh1**: "3123" - Mã nhóm khách hàng cấp 1 (vùng miền)
- **nh_kh2**: "312" - Mã nhóm khách hàng cấp 2 (loại hình)
- **nh_kh3**: "" - Mã nhóm khách hàng cấp 3 (phân hạng)

### **🌍 Tham số vùng miền**

- **rg_code**: "VN" - Mã vùng/khu vực

### **📊 Tham số hiển thị**

- **kieu_xem**: "1" - Kiểu xem báo cáo (1=chuẩn, 2=chi tiết)
- **ct_vt**: 0 - Loại chứng từ vật tư (0=tất cả)
- **group_by**: "230,220,210" - Nhóm theo các tiêu chí
- **ma_unit**: "" - Mã đơn vị
- **mau_bc**: 20 - Mẫu báo cáo số 20

### **🧪 Tham số test (Development)**

- **test**: "true" - Trả về data test thực tế từ script tạo data
- **Mục đích**: Sử dụng cho development và testing
- **Data source**: Từ `create_bang_can_doi_phat_sinh_cong_no_test_data.py`
- **Khác biệt với mock**: Data thực tế được tạo từ script, không phải hard-coded

### **🔍 PHÂN TÍCH CHI TIẾT CÁC THAM SỐ**

#### **1. Extend Metadata trong ERP Request:**

```json
{
  "Name": "tk",
  "DataType": "C",
  "Value": "1312,1311,131",
  "Extend": "FIND|@DFAccountKH|inlike|ÿbcdcnÿFilterÿf1"
}
```

**Giải thích Extend:**

- **FIND**: Chế độ tìm kiếm
- **@DFAccountKH**: Function tìm tài khoản khách hàng
- **inlike**: Tìm kiếm gần đúng
- **ÿbcdcnÿFilterÿf1**: Metadata filter level 1

#### **2. Nhóm khách hàng với SQL Join:**

```json
{
  "Name": "nh_kh1",
  "Extend": "FIND||c.nh_kh1 like '%s%'|left join dmkh c on a.ma_kh = c.ma_kh"
}
```

**Giải thích:**

- **c.nh_kh1 like '%s%'**: Điều kiện WHERE
- **left join dmkh c**: Join với bảng danh mục khách hàng
- **on a.ma_kh = c.ma_kh**: Điều kiện join

#### **3. Vùng miền với hierarchy:**

```json
{
  "Name": "rg_code",
  "Extend": "FIND||c.rg_code in (select rg_code from sysregioninfo a where exists(select 1 from sysregioninfo b where rg_code like '%s%' and a.rg_ref like b.rg_ref + '%'))"
}
```

**Giải thích:**

- **Subquery**: Tìm vùng miền theo hierarchy
- **rg_ref like b.rg_ref + '%'**: Tìm theo cây phân cấp
- **exists**: Kiểm tra tồn tại trong hierarchy

---

## �📋 CÁC CỘT DỮ LIỆU VÀ Ý NGHĨA

### 1. **Tài Khoản (tk)**

- **Giá trị**: "1311", "1312", "131x"
- **Ý nghĩa**: Mã tài khoản kế toán theo hệ thống VAS
- **Liên kết với đầu vào**: Lọc theo parameter `tk`
- **Câu hỏi nghiệp vụ**:
  - ✅ Có sử dụng tài khoản 1311 "Phải thu khách hàng" không?
  - ✅ Có tài khoản nào khác cần theo dõi công nợ không?

### 2. **Mã Khách Hàng (ma_kh)**

- **Giá trị**: "VIP001", "REG001", "NEW001"
- **Ý nghĩa**: Mã định danh duy nhất của khách hàng
- **Câu hỏi nghiệp vụ**:
  - ✅ Quy tắc đặt mã khách hàng như thế nào?
  - ✅ Có phân loại theo VIP/REG/NEW không?

### 3. **Tên Khách Hàng (ten_kh)**

- **Giá trị**: "Công ty TNHH ABC", "Cửa hàng XYZ"
- **Ý nghĩa**: Tên đầy đủ của khách hàng
- **Câu hỏi nghiệp vụ**:
  - ✅ Có yêu cầu đặc biệt về format tên không?

### 4. **Dư Nợ Đầu (no_dk)**

- **Giá trị**: 5,000,000 VND
- **Ý nghĩa**: Số tiền khách hàng nợ công ty tại đầu kỳ báo cáo
- **Công thức**: Tổng số dư nợ tích lũy từ trước đến ngày bắt đầu kỳ
- **Câu hỏi nghiệp vụ**:
  - ✅ Có tính số dư đầu kỳ từ khi nào?
  - ✅ Có loại trừ nợ xấu không?

### 5. **Dư Có Đầu (co_dk)**

- **Giá trị**: 0 VND (thường)
- **Ý nghĩa**: Số tiền công ty nợ khách hàng tại đầu kỳ
- **Trường hợp**: Khách hàng trả trước, hoàn tiền
- **Câu hỏi nghiệp vụ**:
  - ✅ Có trường hợp khách hàng trả trước không?
  - ✅ Xử lý hoàn tiền như thế nào?

### 6. **Phát Sinh Nợ (ps_no)**

- **Giá trị**: 10,000,000 VND
- **Ý nghĩa**: Tổng tiền bán hàng mới trong kỳ (chưa thu)
- **Bao gồm**: Hóa đơn bán hàng, dịch vụ
- **Câu hỏi nghiệp vụ**:
  - ✅ Có tính VAT trong phát sinh nợ không?
  - ✅ Có loại hóa đơn nào không tính không?

### 7. **Phát Sinh Có (ps_co)**

- **Giá trị**: 8,000,000 VND
- **Ý nghĩa**: Tổng tiền đã thu từ khách hàng trong kỳ
- **Bao gồm**: Tiền mặt, chuyển khoản, séc
- **Câu hỏi nghiệp vụ**:
  - ✅ Có phân biệt hình thức thanh toán không?
  - ✅ Có tính chiết khấu thanh toán sớm không?

### 8. **Dư Nợ Cuối (no_ck)**

- **Giá trị**: 7,000,000 VND
- **Ý nghĩa**: Số tiền khách hàng còn nợ tại cuối kỳ
- **Công thức**: `no_ck = no_dk + ps_no - ps_co`
- **Câu hỏi nghiệp vụ**:
  - ✅ Có cảnh báo quá hạn thanh toán không?
  - ✅ Có phân loại theo độ tuổi nợ không?

### 9. **Dư Có Cuối (co_ck)**

- **Giá trị**: 0 VND (thường)
- **Ý nghĩa**: Số tiền công ty còn nợ khách hàng tại cuối kỳ
- **Câu hỏi nghiệp vụ**:
  - ✅ Xử lý số dư có cuối kỳ như thế nào?

---

## 🏷️ PHÂN LOẠI KHÁCH HÀNG (NHÓM)

### **Nhóm 1 - Vùng Địa Lý (nhom1)**

- **VN-NORTH**: Miền Bắc
- **VN-CENTRAL**: Miền Trung
- **VN-SOUTH**: Miền Nam
- **Câu hỏi nghiệp vụ**:
  - ✅ Có phân chia vùng miền khác không?
  - ✅ Có theo tỉnh/thành phố không?

### **Nhóm 2 - Loại Hình Kinh Doanh (nhom2)**

- **BIZ-CORPORATE**: Doanh nghiệp lớn
- **BIZ-WHOLESALE**: Bán sỉ
- **BIZ-RETAIL**: Bán lẻ
- **Câu hỏi nghiệp vụ**:
  - ✅ Có phân loại khách hàng theo quy mô không?
  - ✅ Có nhóm khách hàng đặc biệt không?

### **Nhóm 3 - Phân Hạng Giá Trị (nhom3)**

- **VAL-PREMIUM**: Khách VIP (>2 triệu/giao dịch)
- **VAL-STANDARD**: Khách thường (0.8-2 triệu)
- **VAL-BASIC**: Khách mới (<0.8 triệu)
- **Câu hỏi nghiệp vụ**:
  - ✅ Tiêu chí phân hạng khách hàng là gì?
  - ✅ Có ưu đãi theo từng hạng không?

---

## 💰 VÍ DỤ THỰC TẾ

### **Khách hàng VIP001 - Tháng 1/2025**

| Cột        | Giá trị          | Diễn giải                      |
| ---------- | ---------------- | ------------------------------ |
| **tk**     | 1311             | Tài khoản phải thu khách hàng  |
| **ma_kh**  | VIP001           | Mã khách hàng VIP              |
| **ten_kh** | Công ty TNHH ABC | Tên khách hàng                 |
| **no_dk**  | 5,000,000        | Nợ đầu tháng: 5 triệu          |
| **co_dk**  | 0                | Không có số dư có đầu          |
| **ps_no**  | 10,000,000       | Bán hàng trong tháng: 10 triệu |
| **ps_co**  | 8,000,000        | Thu tiền trong tháng: 8 triệu  |
| **no_ck**  | 7,000,000        | Nợ cuối tháng: 7 triệu         |
| **co_ck**  | 0                | Không có số dư có cuối         |
| **nhom**   | VN-NORTH         | Nhóm chính                     |
| **nhom1**  | Miền Bắc         | Vùng địa lý                    |
| **nhom2**  | Doanh nghiệp     | Loại hình                      |
| **nhom3**  | Khách VIP        | Phân hạng                      |

**Diễn giải**: Khách hàng VIP001 đầu tháng nợ 5 triệu, trong tháng mua thêm 10 triệu, trả 8 triệu, cuối tháng còn nợ 7 triệu.

---

## 🔄 LUỒNG NGHIỆP VỤ

### **Bước 1: Tạo Giao Dịch Bán Hàng**

```
Khi bán hàng cho khách hàng:
1. Ghi tăng công nợ khách hàng (Nợ TK 1311)
2. Ghi tăng doanh thu (Có TK 5111)
```

### **Bước 2: Thu Tiền Khách Hàng**

```
Khi thu tiền từ khách hàng:
1. Ghi tăng tiền mặt/ngân hàng (Nợ TK 111/112)
2. Ghi giảm công nợ khách hàng (Có TK 1311)
```

### **Bước 3: Tính Toán Báo Cáo**

```
Công thức cân đối:
Số dư cuối = Số dư đầu + Phát sinh nợ - Phát sinh có
no_ck = no_dk + ps_no - ps_co
```

---

## ❓ CÂU HỎI NGHIỆP VỤ CẦN XÁC NHẬN

### **1. Về Tài Khoản Kế Toán**

- [ ] Có sử dụng tài khoản 1311 "Phải thu khách hàng" không?
- [ ] Có tài khoản 1312 "Trả trước cho người bán" không?
- [ ] Có tài khoản nào khác cần theo dõi công nợ không?

### **2. Về Phân Loại Khách Hàng**

- [ ] Quy tắc đặt mã khách hàng như thế nào?
- [ ] Có phân loại VIP/REG/NEW không?
- [ ] Tiêu chí phân hạng khách hàng là gì?
- [ ] Có nhóm khách hàng đặc biệt nào không?

### **3. Về Tính Toán Công Nợ**

- [ ] Có tính VAT trong công nợ không?
- [ ] Có loại hóa đơn nào không tính công nợ không?
- [ ] Có tính chiết khấu thanh toán sớm không?
- [ ] Xử lý nợ xấu như thế nào?

### **4. Về Báo Cáo**

- [ ] Kỳ báo cáo thường là bao lâu? (Tháng/Quý/Năm)
- [ ] Có cần phân tích theo độ tuổi nợ không?
- [ ] Có cảnh báo quá hạn thanh toán không?
- [ ] Có xuất báo cáo theo nhóm khách hàng không?

### **5. Về Quy Trình**

- [ ] Ai có quyền xem báo cáo này?
- [ ] Có cần phê duyệt trước khi xuất báo cáo không?
- [ ] Có tích hợp với hệ thống khác không?

---

## �️ CẤU TRÚC DATABASE HIỆN TẠI

### **Bảng Chính Liên Quan**

#### 1. **django_ledger_customermodel** - Khách Hàng

```sql
- uuid (PK)                    -- ID khách hàng
- entity_model_id (FK)         -- Thuộc đơn vị nào
- customer_code                -- Mã khách hàng (VIP001)
- customer_name                -- Tên khách hàng
- customer_group1_id (FK)      -- Nhóm 1 (Vùng miền)
- customer_group2_id (FK)      -- Nhóm 2 (Loại hình)
- customer_group3_id (FK)      -- Nhóm 3 (Phân hạng)
- credit_limit                 -- Hạn mức tín dụng
```

#### 2. **group** - Nhóm Phân Loại

```sql
- uuid (PK)                    -- ID nhóm
- entity_model_id (FK)         -- Thuộc đơn vị nào
- ma_nhom                      -- Mã nhóm (VN-NORTH)
- ten_phan_nhom                -- Tên nhóm (Miền Bắc)
- loai_nhom                    -- Loại nhóm (geographic/business/value)
- trang_thai                   -- Trạng thái (active/inactive)
```

#### 3. **django_ledger_accountmodel** - Tài Khoản

```sql
- uuid (PK)                    -- ID tài khoản
- code                         -- Mã tài khoản (1311)
- name                         -- Tên tài khoản (Phải thu KH)
- role                         -- Vai trò (asset_ca_receivables)
- balance_type                 -- Loại số dư (debit/credit)
```

#### 4. **django_ledger_transactionmodel** - Giao Dịch

```sql
- uuid (PK)                    -- ID giao dịch
- journal_entry_id (FK)        -- Thuộc bút toán nào
- account_id (FK)              -- Tài khoản nào
- tx_type                      -- Loại (debit/credit)
- amount                       -- Số tiền
- description                  -- Mô tả (chứa tên/mã KH)
```

#### 5. **journal_entry** - Bút Toán

```sql
- uuid (PK)                    -- ID bút toán
- date                         -- Ngày bút toán
- description                  -- Mô tả bút toán
- total_amount_debit           -- Tổng ghi nợ
- total_amount_credit          -- Tổng ghi có
- posted                       -- Đã ghi sổ
```

### **Mối Quan Hệ Database**

```
EntityModel (1) -----> (N) CustomerModel
EntityModel (1) -----> (N) GroupModel
CustomerModel (N) ---> (1) GroupModel (3 lần: group1, group2, group3)
AccountModel (1) -----> (N) TransactionModel
JournalEntry (1) -----> (N) TransactionModel
```

---

## 🔍 LOGIC TRUY VẤN HIỆN TẠI

### **Query Chính**

```sql
-- Lấy giao dịch khách hàng từ mô tả
SELECT
    cust.customer_code,
    cust.customer_name,
    acc.code as account_code,
    txn.tx_type,
    txn.amount,
    je.date as transaction_date
FROM django_ledger_customermodel cust
LEFT JOIN django_ledger_transactionmodel txn ON (
    txn.description LIKE '%' || cust.customer_name || '%' OR
    txn.description LIKE '%' || cust.customer_code || '%'
)
LEFT JOIN journal_entry je ON txn.journal_entry_id = je.uuid
LEFT JOIN django_ledger_accountmodel acc ON txn.account_id = acc.uuid
WHERE cust.entity_model_id = ?
  AND acc.code LIKE '131%'
```

### **Tính Toán Số Dư**

```sql
-- Số dư đầu kỳ
SUM(CASE
    WHEN transaction_date < start_date AND tx_type = 'debit' THEN amount
    WHEN transaction_date < start_date AND tx_type = 'credit' THEN -amount
    ELSE 0
END) as opening_balance

-- Phát sinh trong kỳ
SUM(CASE
    WHEN transaction_date BETWEEN start_date AND end_date
         AND tx_type = 'debit' THEN amount
    ELSE 0
END) as period_debit
```

---

## ❗ VẤN ĐỀ KỸ THUẬT CẦN XÁC NHẬN

### **1. Liên Kết Khách Hàng - Giao Dịch**

- **Hiện tại**: Dựa vào mô tả giao dịch (LIKE '%customer_name%')
- **Vấn đề**: Không chính xác, có thể nhầm lẫn
- **Câu hỏi**:
  - [ ] Có trường customer_id trong TransactionModel không?
  - [ ] Có bảng riêng lưu công nợ khách hàng không?

### **2. Tài Khoản Kế Toán**

- **Hiện tại**: Lọc theo code LIKE '131%'
- **Câu hỏi**:
  - [ ] Có sử dụng tài khoản 1311, 1312 không?
  - [ ] Có tài khoản nào khác cần theo dõi không?

### **3. Phân Loại Nhóm**

- **Hiện tại**: 3 cấp nhóm (group1, group2, group3)
- **Câu hỏi**:
  - [ ] Có đủ 3 cấp phân loại không?
  - [ ] Có cần thêm cấp phân loại nào không?

### **4. Kỳ Báo Cáo**

- **Hiện tại**: Theo ngày bắt đầu và kết thúc
- **Câu hỏi**:
  - [ ] Có theo tháng kế toán không?
  - [ ] Có theo năm tài chính không?

---

## ❓ CÂU HỎI NGHIỆP VỤ CHO CÁC THAM SỐ MỚI

### **1. Về Tham Số Thời Gian**

- [ ] Kỳ báo cáo thường là bao lâu? (Ngày/Tháng/Quý/Năm)
- [ ] Có theo năm tài chính hay năm dương lịch?
- [ ] Có cần báo cáo theo kỳ kế toán không?
- [ ] Format ngày có đúng YYYYMMDD không?

### **2. Về Tham Số Tài Khoản (tk)**

- [ ] Có cho phép lọc nhiều tài khoản cùng lúc không?
- [ ] Danh sách tài khoản "1312,1311,131" có đúng không?
- [ ] Có tài khoản nào khác cần bổ sung không?

### **3. Về Tham Số Khách Hàng (ma_kh, nh_kh1-3)**

- [ ] 3 cấp nhóm khách hàng có đủ không? (nh_kh1, nh_kh2, nh_kh3)
- [ ] Có cho phép tìm kiếm gần đúng theo tên khách hàng không?
- [ ] Ý nghĩa của các mã nhóm "3123", "312" là gì?

### **4. Về Vùng Miền (rg_code)**

- [ ] Có phân chia theo vùng miền không?
- [ ] Cấu trúc phân cấp vùng miền như thế nào?
- [ ] Có sử dụng mã vùng VN (Vietnam) không?
- [ ] Có vùng miền nào khác cần theo dõi không?

### **5. Về Tham Số Hiển Thị**

- [ ] Kiểu xem báo cáo: 1=chuẩn, 2=chi tiết có đúng không?
- [ ] Có cần phân biệt chứng từ vật tư (ct_vt) không?
- [ ] Group_by theo các tiêu chí nào? (230,220,210 có ý nghĩa gì?)
- [ ] Mẫu báo cáo số 20 có đúng format không?
- [ ] Có cần thêm mẫu báo cáo nào khác không?

### **6. Về Extend Metadata**

- [ ] Có cần xử lý các Extend metadata phức tạp không?
- [ ] SQL Join trong Extend có cần implement không?
- [ ] Function @DFAccountKH có tồn tại trong hệ thống không?

---

## 📋 BẢNG MAPPING ERP PARAMETERS VÀ IMPLEMENTATION

| ERP Parameter | Giá trị mẫu     | Implementation                    | Status  |
| ------------- | --------------- | --------------------------------- | ------- |
| **ngay_ct1**  | "********"      | Date parsing YYYYMMDD             | ✅ Done |
| **ngay_ct2**  | "********"      | Date parsing YYYYMMDD             | ✅ Done |
| **tk**        | "1312,1311,131" | Split comma, filter accounts      | ✅ Done |
| **ma_kh**     | "A"             | LIKE '%A%' customer search        | ✅ Done |
| **nh_kh1**    | "3123"          | Filter by customer_group1         | ✅ Done |
| **nh_kh2**    | "312"           | Filter by customer_group2         | ✅ Done |
| **nh_kh3**    | ""              | Filter by customer_group3         | ✅ Done |
| **rg_code**   | "VN"            | Region hierarchy filter           | ❌ TODO |
| **kieu_xem**  | "1"             | View type (1=standard, 2=detail)  | ❌ TODO |
| **ct_vt**     | 0               | Transaction type filter           | ❌ TODO |
| **group_by**  | "230,220,210"   | Group by criteria                 | ❌ TODO |
| **ma_unit**   | ""              | Unit filter                       | ❌ TODO |
| **mau_bc**    | 20              | Report template number            | ❌ TODO |
| **test**      | "true"          | Return real test data from script | ✅ Done |

### **🎯 PRIORITY IMPLEMENTATION:**

1. **High Priority** (Core functionality):

   - ✅ ngay_ct1, ngay_ct2 (Date range)
   - ✅ tk (Account filter)
   - ✅ ma_kh (Customer filter)
   - ✅ nh_kh1-3 (Customer groups)

2. **Medium Priority** (Enhanced features):

   - ❌ rg_code (Region hierarchy)
   - ❌ kieu_xem (View types)
   - ❌ group_by (Grouping)

3. **Low Priority** (Advanced features):
   - ❌ ct_vt (Transaction types)
   - ❌ ma_unit (Unit filter)
   - ❌ mau_bc (Report templates)

---

## �📊 KẾT LUẬN

Đây là bản phân tích chi tiết chức năng "Bảng Cân Đối Phát Sinh Công Nợ Khách Hàng" bao gồm:

1. **Nghiệp vụ**: Ý nghĩa các cột, luồng xử lý
2. **Kỹ thuật**: Cấu trúc database, logic truy vấn
3. **Câu hỏi**: Các điểm cần xác nhận với nghiệp vụ

**Vui lòng xác nhận các thông tin trên và bổ sung các yêu cầu nghiệp vụ cụ thể để đảm bảo hệ thống phù hợp với thực tế hoạt động.**

---

## 📞 LIÊN HỆ

**Người phân tích**: Development Team
**Ngày tạo**: $(date)
**Phiên bản**: 1.0

_File này sẽ được cập nhật sau khi có phản hồi từ bộ phận nghiệp vụ._
