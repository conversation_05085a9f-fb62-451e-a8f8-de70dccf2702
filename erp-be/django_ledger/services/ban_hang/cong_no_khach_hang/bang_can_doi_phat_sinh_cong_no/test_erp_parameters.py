#!/usr/bin/env python3
"""
🎯 TEST ERP PARAMETERS FOR DEBT BALANCE REPORT
==============================================
Test the new optimized service with real ERP parameters.

Usage:
    python test_erp_parameters.py
"""

import json
import requests
from datetime import datetime


def test_erp_parameters():
    """Test the debt balance API with real ERP parameters."""
    
    # Real ERP parameters from the request
    erp_parameters = {
        "ngay_ct1": "20250101",
        "ngay_ct2": "20250603", 
        "tk": "1312,1311,131",
        "kieu_xem": "1",
        "ct_vt": 0,
        "ma_kh": "A",
        "nh_kh1": "3123",
        "nh_kh2": "312", 
        "nh_kh3": "",
        "rg_code": "VN",
        "group_by": "230,220,210",
        "ma_unit": "",
        "mau_bc": 20
    }
    
    # API endpoint
    url = "http://localhost:8003/api/entities/my-new-company-zuufe21o/erp/ban-hang/cong-no-khach-hang/bang-can-doi-phat-sinh-cong-no/"
    
    print("🎯 TESTING ERP PARAMETERS")
    print("=" * 50)
    print(f"URL: {url}")
    print(f"Parameters: {json.dumps(erp_parameters, indent=2)}")
    print()
    
    try:
        # Make API request
        response = requests.post(
            url,
            json=erp_parameters,
            auth=('admin', 'minhdang123'),
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ SUCCESS: {data['count']} records returned")
            print()
            
            # Display first few records
            for i, record in enumerate(data['results'][:3]):
                print(f"📊 Record {i+1}:")
                print(f"   ma_kh: {record['ma_kh']}")
                print(f"   ten_kh: {record['ten_kh']}")
                print(f"   tk: {record['tk']}")
                print(f"   ps_no: {record['ps_no']:,.2f}")
                print(f"   ps_co: {record['ps_co']:,.2f}")
                print(f"   no_ck: {record['no_ck']:,.2f}")
                print(f"   nhom: '{record['nhom']}'")
                print(f"   nhom1: '{record['nhom1']}'")
                print(f"   nhom2: '{record['nhom2']}'")
                print(f"   nhom3: '{record['nhom3']}'")
                print()
                
        else:
            print(f"❌ ERROR: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")


def test_simple_parameters():
    """Test with simplified parameters."""
    
    simple_parameters = {
        "ngay_ct1": "20250101",
        "ngay_ct2": "20250603"
    }
    
    url = "http://localhost:8003/api/entities/my-new-company-zuufe21o/erp/ban-hang/cong-no-khach-hang/bang-can-doi-phat-sinh-cong-no/"
    
    print("🎯 TESTING SIMPLE PARAMETERS")
    print("=" * 50)
    print(f"Parameters: {json.dumps(simple_parameters, indent=2)}")
    print()
    
    try:
        response = requests.post(
            url,
            json=simple_parameters,
            auth=('admin', 'minhdang123'),
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ SUCCESS: {data['count']} records returned")
            
            # Show summary
            if data['results']:
                total_ps_no = sum(float(r['ps_no']) for r in data['results'])
                total_ps_co = sum(float(r['ps_co']) for r in data['results'])
                total_no_ck = sum(float(r['no_ck']) for r in data['results'])
                
                print(f"📊 SUMMARY:")
                print(f"   Total ps_no: {total_ps_no:,.2f}")
                print(f"   Total ps_co: {total_ps_co:,.2f}")
                print(f"   Total no_ck: {total_no_ck:,.2f}")
                print()
                
        else:
            print(f"❌ ERROR: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")


def test_customer_filter():
    """Test with customer filter."""
    
    customer_filter_parameters = {
        "ngay_ct1": "20250101",
        "ngay_ct2": "20250603",
        "ma_kh": "VIP"  # Filter for VIP customers
    }
    
    url = "http://localhost:8003/api/entities/my-new-company-zuufe21o/erp/ban-hang/cong-no-khach-hang/bang-can-doi-phat-sinh-cong-no/"
    
    print("🎯 TESTING CUSTOMER FILTER")
    print("=" * 50)
    print(f"Parameters: {json.dumps(customer_filter_parameters, indent=2)}")
    print()
    
    try:
        response = requests.post(
            url,
            json=customer_filter_parameters,
            auth=('admin', 'minhdang123'),
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ SUCCESS: {data['count']} VIP customers returned")
            
            for record in data['results']:
                print(f"   {record['ma_kh']}: {record['ten_kh']} - {record['no_ck']:,.2f}")
                
        else:
            print(f"❌ ERROR: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")


if __name__ == "__main__":
    print("🚀 ERP DEBT BALANCE REPORT TESTING")
    print("=" * 60)
    print(f"Timestamp: {datetime.now()}")
    print()
    
    # Test 1: Simple parameters
    test_simple_parameters()
    print()
    
    # Test 2: Customer filter
    test_customer_filter()
    print()
    
    # Test 3: Full ERP parameters
    test_erp_parameters()
    
    print("🎉 TESTING COMPLETE!")
