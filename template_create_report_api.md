# 🚀 Template Tạo API Báo C<PERSON>o (<PERSON><PERSON><PERSON> Ke <PERSON>hap Xu<PERSON>)

## 📋 Template Sử Dụng Nhanh

Copy và điền thông tin vào template dưới đây:

---

````
Tôi muốn tạo API báo cáo "[TÊN_BÁO_CÁO]" theo chuẩn bang_ke_nhap_xuat_kho với thông tin sau:

**1. cURL request (POST method):**
```bash
[PASTE_CURL_COMMAND_HERE]
````

**2. Response fields cần hiển thị:**

```json
[
  "stt",
  "field_name_1",
  "field_name_2",
  "field_name_3",
  "field_name_4",
  "field_name_5"
]
```

**3. Tên chức năng:** [TÊN_CHỨC_NĂNG_ĐẦY_ĐỦ]

**4. Module structure:**

- Module: [MODULE_NAME] (ví dụ: ton_kho, tai_san, ban_hang)
- Sub-module: [SUB_MODULE_NAME] (ví dụ: tinh_hinh_nhap_xuat_kho, kiem_ke_tscd)
- Report name: [REPORT_NAME] (ví dụ: bang_ke_nhap_xuat_kho, the_tscd)

**5. Endpoint mong muốn:**
`/api/entities/{entity_slug}/erp/[module]/[sub-module]/[report-name]/`

Hãy triển khai API báo cáo theo chuẩn bang_ke_nhap_xuat_kho pattern với đầy đủ:

- Service layer với lazy import và comprehensive filtering
- Serializer với detailed field validation và response formatting
- ViewSet với get_report method và ERPPagination
- Utils layer với field_mappers, data_processors,
- SQL note file và warning.md documentation
- Comprehensive test data creation script
- Auto API testing với sample filters

```

---

## 🏗️ Implementation Checklist

### **Phase 0: Deep Analysis (MANDATORY - NEW)**
- [ ] **Database Schema Deep Dive** - `python manage.py shell -c "from django.db import connection; cursor = connection.cursor(); cursor.execute('PRAGMA table_info(table_name)'); print(cursor.fetchall())"`
- [ ] **Foreign Key Mapping** - Map ALL relationships between tables
- [ ] **Field Type Validation** - UUID vs String vs Integer vs Decimal vs Boolean
- [ ] **Business Logic Understanding** - Understand calculation rules and constraints
- [ ] **Existing Pattern Study** - Find similar working reports and copy exact patterns
- [ ] **Error Scenarios Planning** - Plan for null values, missing data, edge cases

### **Phase 1: Analysis & Planning**
- [ ] **Database Schema Validation** - Check schema first: `sqlite3 db.sqlite3 ".schema table_name"`
- [ ] Phân tích cURL request để extract tất cả filter parameters
- [ ] Phân tích response fields để xác định data sources
- [ ] Map fields với db_schema.txt để tìm relationships
- [ ] Xác định main tables và foreign key dependencies
- [ ] **Identify Main + Detail Tables** - Critical for transaction reports
- [ ] **Validate Field Types** - UUID vs String vs Integer vs Decimal

### **Phase 2: Core API Structure**
- [ ] Tạo RequestSerializer với comprehensive field validation
- [ ] Tạo ResponseSerializer với tất cả response fields
- [ ] Tạo Service class với lazy import pattern
- [ ] Tạo ViewSet với get_report method và ERPPagination
- [ ] Tạo Router với POST method mapping

### **Phase 3: Business Logic Implementation**
- [ ] Implement filter logic trong service layer
- [ ] Tạo data processing utilities (field_mappers, data_processorsb)
- [ ] Handle UUID format consistency
- [ ] Implement safe attribute access với default values
- [ ] Add comprehensive error handling

### **Phase 4: Documentation & SQL**
- [ ] Tạo [report_name]_query.sql với detailed SQL logic
- [ ] Tạo warning.md với field mapping analysis
- [ ] Document performance considerations
- [ ] Add business logic explanations

### **Phase 5: Enhanced Test Data & Validation**
- [ ] **Database Path Handling** - Relative path: `../../../../../db.sqlite3`
- [ ] **Dependency Creation Order** - Entity → Basic deps → Master data → Transactions
- [ ] **Complete Transaction Creation** - ALWAYS create main + detail records
- [ ] **Comprehensive Test Data Script** - create_[report_name]_test_data.py with ALL required fields
- [ ] **Test Data Validation** - Validate business logic compliance (balance equations, etc.)
- [ ] **Error Handling in Test Data** - Handle IntegrityError, foreign key violations
- [ ] **API Testing Script** - create_test_[report_name]_parameters.py with multiple scenarios
- [ ] **Immediate API Testing** - Test API right after data creation
- [ ] **Response Validation** - Verify no null/empty fields, proper data types
- [ ] **Performance Testing** - Response time < 2 seconds
- [ ] **Edge Case Testing** - Test with empty data, invalid parameters

---

## 📁 Folder Structure Chuẩn

```

services/[module]/[sub_module]/[report_name]/
├── **init**.py
├── [report_name].py # Main service class
├── [report_name]_query.sql # SQL logic documentation
├── create_[report_name]_test_data.py # Comprehensive test data script
├── create_[report_name]\_test_data.sh # Shell script runner
├── warning.md # Field mapping analysis
└── utils/ # Utils folder within service (3-file structure)
├── **init**.py # Clean exports
├── sql_queries.py # SQL templates & constants
├── query_builders.py # Dynamic query building
└── data_transformers.py # Data transformation logic

api/serializers/[module]/[sub_module]/[report_name]/
├── **init**.py
└── [report_name].py # Request & Response serializers

api/views/[module]/[sub_module]/[report_name]/
├── **init**.py
└── [report_name].py # ViewSet implementation

api/routers/[module]/[sub_module]/[report_name]/
└── urls.py # URL routing

````

---

## 🎯 Core Patterns (Updated với Lessons Learned)

### **1. Service Pattern (Simplified & Robust):**
```python
class [ReportName]Service(BaseService):
    """Service class for [ReportName] business logic."""

    def __init__(self):
        super().__init__()
        # Keep minimal dependencies - avoid complex lazy imports

    def generate_report(self, entity_slug: str, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Main orchestration method."""
        try:
            # Get filtered data using utils
            data = self._get_filtered_data(entity_slug, filters)
            # Process and format using utils
            return self._process_report_data(data)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error generating report: {str(e)}", exc_info=True)
            return []

    def _get_filtered_data(self, entity_slug: str, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get filtered data using direct SQL with utils."""
        from django.db import connection
        from django_ledger.models import EntityModel
        from .utils.query_builders import build_complete_query

        # Get entity UUID - always filter by entity first
        try:
            entity = EntityModel.objects.get(slug=entity_slug)
            entity_uuid = str(entity.uuid).replace('-', '')
        except EntityModel.DoesNotExist:
            return []

        # Build complete query using utils
        sql_query, sql_params = build_complete_query(entity_uuid, filters)

        # Execute query with proper error handling
        with connection.cursor() as cursor:
            cursor.execute(sql_query, sql_params)
            columns = [col[0] for col in cursor.description]
            results = []

            for row in cursor.fetchall():
                row_dict = dict(zip(columns, row))
                results.append(row_dict)

            return results

    def _process_report_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process data using utils transformers."""
        from .utils.data_transformers import transform_report_data
        return transform_report_data(data)
````

### **2. Serializer Pattern:**

```python
class [ReportName]RequestSerializer(serializers.Serializer):
    # Core filters with comprehensive validation
    ngay_ct1 = serializers.DateField(required=True)
    ngay_ct2 = serializers.DateField(required=True)

    # UUID filters with CharField flexibility
    ma_field = serializers.CharField(required=False, allow_blank=True, max_length=50)

    # Standard parameters
    ma_unit = serializers.UUIDField(required=False, allow_null=True)
    mau_bc = serializers.IntegerField(required=False, default=20)

    def validate(self, data):
        # Business rule validation
        return data

class [ReportName]ResponseSerializer(serializers.Serializer):
    stt = serializers.IntegerField()
    field1 = serializers.CharField(max_length=50)
    field2 = serializers.DecimalField(max_digits=15, decimal_places=3)
    # Add all response fields with proper types
```

### **3. ViewSet Pattern:**

```python
@extend_schema_view(...)
class [ReportName]ViewSet(viewsets.ViewSet):
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination

    def get_report(self, request, entity_slug):
        # Validate, process, paginate, serialize
        serializer = [ReportName]RequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(errors, status=400)

        report_data = self.service.generate_report(entity_slug, serializer.validated_data)
        paginated_data = self.pagination_class().paginate_queryset(report_data, request)
        response_serializer = [ReportName]ResponseSerializer(paginated_data, many=True)
        return self.pagination_class().get_paginated_response(response_serializer.data)
```

### **4. Enhanced Utils Pattern (3-File Structure):**

```python
# utils/__init__.py - Clean exports with error handling
try:
    from .sql_queries import (
        ReportSQLQueries,  # Use class-based approach
        FIELD_MAP,
    )
    from .test_data_utils import (
        ReportTestDataUtils,  # Comprehensive test data utilities
    )
    from .data_transformers import (
        transform_report_item,
        transform_report_data,
        validate_response_data,  # NEW: Response validation
    )
except ImportError as e:
    # Handle import errors gracefully
    print(f"Warning: Utils import error: {e}")
    pass

# utils/sql_queries.py - SQL templates & constants
MAIN_REPORT_QUERY = """
    SELECT
        main.field1 as field1,
        main.field2 as field2,
        detail.field3 as field3
    FROM main_table main
    INNER JOIN detail_table detail ON main.uuid = detail.main_id
    WHERE {conditions}
    ORDER BY main.date_field, main.doc_number
"""

FIELD_MAP = {
    'field1': 'main.field1',
    'field2': 'main.field2',
    'field3': 'detail.field3',
}

def build_report_query(conditions: str) -> str:
    return MAIN_REPORT_QUERY.format(conditions=conditions)

# utils/query_builders.py - Dynamic query building
def build_period_conditions(filters: Dict[str, Any]) -> Tuple[List[str], List[Any]]:
    """Build period-based WHERE conditions."""
    conditions = []
    params = []

    if filters.get("date_from") and filters.get("date_to"):
        conditions.extend(["main.date_field >= %s", "main.date_field <= %s"])
        params.extend([filters["date_from"], filters["date_to"]])

    return conditions, params

def build_complete_query(entity_uuid: str, filters: Dict[str, Any]) -> Tuple[str, List[Any]]:
    """Build complete SQL query with all conditions."""
    # Always start with entity filter
    all_conditions = ["main.entity_model_id = %s"]
    all_params = [entity_uuid]

    # Add period conditions
    period_conditions, period_params = build_period_conditions(filters)
    all_conditions.extend(period_conditions)
    all_params.extend(period_params)

    # Build final query
    conditions_string = ' AND '.join(all_conditions)
    query = build_report_query(conditions_string)

    return query, all_params

# utils/data_transformers.py - Data transformation
def transform_report_item(item: Dict[str, Any], index: int) -> Dict[str, Any]:
    """Transform single item to API response format."""
    return {
        'stt': index,
        'field1': item.get('field1', ''),
        'field2': float(item.get('field2', 0)),
        'field3': item.get('field3', ''),
        # Map ALL serializer fields with proper types
    }

def transform_report_data(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Transform list of data to API response format."""
    return [
        transform_report_item(item, index)
        for index, item in enumerate(data, 1)
    ]

# data_processors.py - Essential functions only
def process_import_transactions(import_details: List[Any]) -> List[Dict[str, Any]]:
    """Process import transaction details into standardized format."""
    processed_transactions = []
    for i, detail in enumerate(import_details, 1):
        mapped_data = map_import_transaction_fields(detail, i, 1)
        if validate_mapped_fields(mapped_data):
            processed_transactions.append(mapped_data)
    return processed_transactions

def combine_and_sort_transactions(import_transactions: List[Dict[str, Any]],
                                export_transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Combine import and export transactions and sort by date."""
    all_transactions = import_transactions + export_transactions
    return sorted(all_transactions, key=lambda x: (x.get('ngay_ct', ''), x.get('so_ct', '')))
```

---

## 📊 Response Format Chuẩn

```json
{
  "count": 150,
  "next": "http://localhost:8001/api/entities/entity-slug/erp/[module]/[sub-module]/[report-name]/?page=2",
  "previous": null,
  "results": [
    {
      "stt": 1,
      "field1": "value1",
      "field2": "123.456",
      "field3": "2024-12-19",
      "field4": "description"
    }
  ]
}
```

---

## 🎯 Key Success Factors

1. **📋 Comprehensive Field Mapping**: Map tất cả fields từ cURL request đến database
2. **🔧 UUID Format Consistency**: Ensure UUID format matching giữa foreign keys và primary keys
3. **🛡️ Safe Attribute Access**: Handle null/empty values gracefully với get_safe_attribute
4. **📊 Complete Test Data**: Tạo đầy đủ reference data để response không empty
5. **📝 Documentation**: SQL notes và field mapping warnings
6. **🧪 Auto Testing**: Test API ngay sau khi tạo data
7. **🧹 Clean Utils**: Chỉ giữ essential functions, loại bỏ unused code
8. **🏗️ Lazy Loading**: Avoid circular imports với lazy service initialization

---

## 💡 Ví Dụ Cụ Thể

````
Tôi muốn tạo API báo cáo "Báo Cáo Tồn Kho Theo Kho" theo chuẩn bang_ke_nhap_xuat_kho với thông tin sau:

**1. cURL request (POST method):**
```bash
curl 'http://localhost:8001/api/entities/my-company/erp/ton-kho/bao-cao-ton-kho/bao-cao-ton-kho-theo-kho/' \
  -H 'Content-Type: application/json' \
  -u 'admin:password' \
  --data-raw '{
    "ngay_ct1": "2024-01-01",
    "ngay_ct2": "2024-12-31",
    "ma_kho": "uuid-kho-1",
    "ma_vt": "uuid-vat-tu-1",
    "ma_bp": "uuid-bo-phan-1"
  }'
````

**2. Response fields cần hiển thị:**

```json
[
  "stt",
  "ma_kho",
  "ten_kho",
  "ma_vt",
  "ten_vt",
  "sl_dau_ky",
  "sl_nhap",
  "sl_xuat",
  "sl_cuoi_ky",
  "gia_tri_ton"
]
```

**3. Tên chức năng:** Báo Cáo Tồn Kho Theo Kho

**4. Module structure:**

- Module: ton_kho
- Sub-module: bao_cao_ton_kho
- Report name: bao_cao_ton_kho_theo_kho

**5. Endpoint mong muốn:**
`/api/entities/{entity_slug}/erp/ton-kho/bao-cao-ton-kho/bao-cao-ton-kho-theo-kho/`

Hãy triển khai API báo cáo theo chuẩn bang_ke_nhap_xuat_kho pattern với đầy đủ:

- Service layer với lazy import và comprehensive filtering
- Serializer với detailed field validation và response formatting
- ViewSet với get_report method và ERPPagination
- Utils layer với field_mappers, data_processors (essential functions only)
- SQL note file và warning.md documentation
- Comprehensive test data creation script
- Auto API testing với sample filters

````

---

## 🚨 Lưu Ý Quan Trọng

### **Template Usage:**
1. **Copy template** và thay thế tất cả placeholders
2. **Paste cURL command** chính xác từ requirement
3. **List tất cả response fields** cần thiết
4. **Xác định module structure** rõ ràng
5. **Submit prompt** với đầy đủ thông tin

### **Quality Assurance:**
- ✅ **Field Mapping**: Tất cả fields phải có mapping rõ ràng
- ✅ **UUID Consistency**: Format UUID phải consistent
- ✅ **Error Handling**: Handle tất cả edge cases
- ✅ **Performance**: Optimize queries và avoid N+1 problems
- ✅ **Documentation**: SQL notes và field warnings đầy đủ
- ✅ **Testing**: Auto test với comprehensive data

### **Success Metrics:**
- 🎯 **API Response**: 200 status với data đầy đủ
- 🎯 **Field Completeness**: Không có null/empty fields
- 🎯 **Performance**: Response time < 2 seconds
- 🎯 **Documentation**: SQL notes và warnings complete
- 🎯 **Test Coverage**: Comprehensive test data và scenarios

---

**Endpoint Pattern**: `/api/entities/{entity_slug}/erp/[module]/[sub-module]/[report-name]/`
**Method**: POST với JSON body filters
**Response**: DRF paginated với comprehensive field data
**Standard**: Bang Ke Nhap Xuat Kho pattern compliance

---

## 🔧 **Data Creation Pattern (Updated)**

### **5. Comprehensive Test Data Script:**

```python
#!/usr/bin/env python3
"""
Comprehensive ERP Report Test Data Creation Script
Creates complete test data with full ERP compliance and relationships.
"""

import os
import sys
import sqlite3
import uuid
import random
from datetime import datetime, date, timedelta
from decimal import Decimal

def get_db_connection():
    """Get database connection with correct path handling."""
    # Handle relative path from service folder
    db_path = os.path.join(os.path.dirname(__file__), '../../../../../db.sqlite3')
    return sqlite3.connect(db_path)

def get_entity_uuid(entity_slug):
    """Get entity UUID from slug."""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute("SELECT uuid FROM django_ledger_entitymodel WHERE slug = ?", (entity_slug,))
    result = cursor.fetchone()
    conn.close()

    if result:
        return result[0]
    else:
        raise Exception(f"Entity with slug '{entity_slug}' not found")

def create_master_data(entity_uuid):
    """Create comprehensive master data for ERP system."""
    conn = get_db_connection()
    cursor = conn.cursor()

    master_data = {
        'customers': [],
        'products': [],
        'accounts': [],
        'tax_codes': []
    }

    try:
        print("🏗️ Creating comprehensive master data...")

        # 1. Create required master data with ALL fields
        # 2. Handle foreign key relationships properly
        # 3. Use proper business logic for amounts and dates
        # 4. Return structured master data for transaction creation

        conn.commit()
        print("✅ Master data creation completed!")
        return master_data

    except Exception as e:
        conn.rollback()
        print(f"❌ Error creating master data: {e}")
        return master_data
    finally:
        conn.close()

def create_comprehensive_transaction(entity_uuid, master_data, transaction_date, doc_number):
    """Create comprehensive transaction with ALL required fields."""
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # 1. Generate proper UUIDs
        # 2. Use master data for relationships
        # 3. Calculate business logic (taxes, totals)
        # 4. Insert with ALL table fields (not just a few)
        # 5. Create both main and detail records

        conn.commit()
        print(f"✅ Created transaction: {doc_number}")
        return True

    except Exception as e:
        conn.rollback()
        print(f"❌ Error creating transaction {doc_number}: {e}")
        return False
    finally:
        conn.close()

def create_comprehensive_data(entity_slug, num_records=10):
    """Create comprehensive ERP data."""
    print("🚀 Creating Comprehensive ERP Data")
    print("=" * 60)

    try:
        # Get entity UUID
        entity_uuid = get_entity_uuid(entity_slug)
        print(f"✅ Entity: {entity_slug} -> {entity_uuid}")

        # Create master data FIRST
        master_data = create_master_data(entity_uuid)

        # Validate master data
        if not master_data['customers']:
            print("❌ Failed to create required master data")
            return False

        # Create transaction data with proper relationships
        created_count = 0
        base_date = date.today()

        for i in range(num_records):
            transaction_date = base_date + timedelta(days=i)
            doc_number = f"DOC{transaction_date.strftime('%Y%m%d')}{i+1:03d}"

            if create_comprehensive_transaction(entity_uuid, master_data, transaction_date, doc_number):
                created_count += 1

        print(f"\n🎉 Successfully created {created_count} records!")

        # Test API immediately
        if created_count > 0:
            test_api_with_data(entity_slug)

        return created_count > 0

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_with_data(entity_slug):
    """Test API with created data."""
    import subprocess
    import json

    test_cases = [
        {
            "name": "All records",
            "data": {
                "date_from": "2024-01-01",
                "date_to": "2024-12-31",
                # Add other filters
            }
        }
    ]

    for test_case in test_cases:
        print(f"\n🧪 Testing: {test_case['name']}")

        cmd = [
            'curl', '-X', 'POST',
            f'http://localhost:8002/api/entities/{entity_slug}/erp/[module]/[sub-module]/[report-name]/',
            '-H', 'Content-Type: application/json',
            '-u', 'admin:minhdang123',
            '--data-raw', json.dumps(test_case['data'])
        ]

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                response = json.loads(result.stdout)
                count = response.get('count', 0)
                print(f"  ✅ {test_case['name']}: Found {count} records")
            else:
                print(f"  ❌ {test_case['name']}: {result.stderr}")
        except Exception as e:
            print(f"  ❌ {test_case['name']}: {e}")

if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='Create comprehensive ERP test data')
    parser.add_argument('entity_slug', help='Entity slug')
    parser.add_argument('--count', type=int, default=10, help='Number of records to create')

    args = parser.parse_args()

    success = create_comprehensive_data(args.entity_slug, args.count)

    if success:
        print(f"\n🎯 SUCCESS! Data created for {args.entity_slug}")
    else:
        print("❌ Failed to create data")

    sys.exit(0 if success else 1)
````

---

## ⚠️ **Common Pitfalls & Solutions (From Lessons Learned)**

### **1. Service Layer Issues:**

- ❌ **Complex lazy imports** → ✅ **Minimal dependencies, direct imports**
- ❌ **ORM complexity** → ✅ **Direct SQL with utils**
- ❌ **Missing entity filtering** → ✅ **Always filter by entity_uuid first**

### **2. Serializer Mismatch:**

- ❌ **Incomplete field mapping** → ✅ **Map ALL serializer fields**
- ❌ **Type mismatches** → ✅ **Proper type conversion in transformers**

### **3. Data Creation Issues:**

- ❌ **Incomplete table structure** → ✅ **Insert ALL required fields**
- ❌ **Missing master data** → ✅ **Create master data first**
- ❌ **Wrong database path** → ✅ **Relative path handling**

### **4. Query Performance:**

- ❌ **N+1 queries** → ✅ **Single SQL with JOINs**
- ❌ **Missing indexes** → ✅ **Proper WHERE clause order**

---

## 🎯 **Success Validation Checklist**

### **API Response Requirements:**

- [ ] ✅ Status 200 OK
- [ ] ✅ Response time < 2s
- [ ] ✅ No null fields in response
- [ ] ✅ Proper pagination format
- [ ] ✅ All serializer fields present

### **Data Quality Requirements:**

- [ ] ✅ All table constraints satisfied
- [ ] ✅ Proper foreign key relationships
- [ ] ✅ Business logic compliance
- [ ] ✅ Realistic test data volumes (10+ records)

### **Code Quality Requirements:**

- [ ] ✅ Utils properly organized (3-file structure)
- [ ] ✅ Error handling implemented
- [ ] ✅ Database path handling correct
- [ ] ✅ Clean imports and dependencies

---

## 🔧 **Troubleshooting Guide (From Real Experience)**

### **No Data Returned:**

- [ ] Check if main records exist: `SELECT COUNT(*) FROM main_table WHERE entity_model_id = 'xxx'`
- [ ] **CRITICAL**: Check if detail records exist: `SELECT COUNT(*) FROM detail_table`
- [ ] Verify date range covers actual data
- [ ] Test with wide date range: "2020-01-01" to "2030-12-31"
- [ ] Check entity filtering is correct
- [ ] Verify service query logic matches data structure

### **API Errors:**

- [ ] Check authentication credentials
- [ ] Verify request format (JSON, headers)
- [ ] Check all required fields are provided
- [ ] Review server logs for detailed errors
- [ ] Test with minimal request first
- [ ] **Validate field types** - UUID vs String confusion is common

### **Data Creation Issues:**

- [ ] **Check database schema first**: `sqlite3 db.sqlite3 ".schema table_name"`
- [ ] Verify entity exists and is accessible
- [ ] **Check foreign key constraints** - NOT NULL constraint failures
- [ ] **Review dependency creation order** - Entity → Basic deps → Master → Transactions
- [ ] **Validate all required fields** - Use PRAGMA table_info
- [ ] **Create complete transactions** - Main + Detail records

### **Field Mapping Issues:**

- [ ] **Verify database schema** matches expectations exactly
- [ ] Check field names are exactly correct (ma_nv vs ma_nhan_vien)
- [ ] **Validate data types** - UUID, String, Integer, Decimal
- [ ] Test field transformations
- [ ] **Ensure all serializer fields are mapped** - No missing fields

```

```
